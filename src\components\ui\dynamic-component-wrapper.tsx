"use client";

import React, { Component, ReactNode, Suspense } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  componentName?: string;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

class DynamicComponentErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(`Error in dynamic component ${this.props.componentName || 'Unknown'}:`, error, errorInfo);
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
    });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Card className="border-destructive/50">
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            <AlertTriangle className="h-8 w-8 text-destructive mb-4" />
            <h3 className="font-semibold text-destructive mb-2">
              Failed to load {this.props.componentName || 'component'}
            </h3>
            <p className="text-sm text-muted-foreground mb-4">
              There was an error loading this component. This might be due to a network issue.
            </p>
            <Button onClick={this.handleRetry} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      );
    }

    return (
      <Suspense fallback={this.props.fallback || <div>Loading...</div>}>
        {this.props.children}
      </Suspense>
    );
  }
}

export function DynamicComponentWrapper({ 
  children, 
  fallback, 
  componentName 
}: Props) {
  return (
    <DynamicComponentErrorBoundary 
      fallback={fallback} 
      componentName={componentName}
    >
      {children}
    </DynamicComponentErrorBoundary>
  );
}
