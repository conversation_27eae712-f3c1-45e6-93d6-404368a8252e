"use client";

import React, { useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target, 
  BarChart3,
  PieChart,
  Activity,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface PerformanceMetricsProps {
  portfolioData: {
    totalPortfolioValueUSDT: number;
    totalSpotValueUSDT: number;
    totalFuturesValueUSDT: number;
    spotPnl24hUSDT: number;
    spotPnl24hPercentage: number;
    assets: any[];
  } | null;
  positionsData: {
    positions: any[];
    totalUnrealizedFuturesPnlUSDT: number;
  } | null;
  isLoading?: boolean;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeType?: 'percentage' | 'absolute';
  icon: React.ReactNode;
  trend?: 'up' | 'down' | 'neutral';
  subtitle?: string;
  progress?: number;
  className?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  changeType = 'percentage',
  icon,
  trend = 'neutral',
  subtitle,
  progress,
  className
}) => {
  const trendColors = {
    up: 'text-green-400',
    down: 'text-red-400',
    neutral: 'text-muted-foreground'
  };

  const trendIcons = {
    up: <TrendingUp className="h-3 w-3" />,
    down: <TrendingDown className="h-3 w-3" />,
    neutral: <Activity className="h-3 w-3" />
  };

  const formatChange = (value: number) => {
    if (changeType === 'percentage') {
      return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
    }
    return `${value >= 0 ? '+' : ''}$${Math.abs(value).toLocaleString()}`;
  };

  return (
    <Card className={cn("transition-smooth hover:shadow-md", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="h-4 w-4 text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="text-2xl font-bold">
            {typeof value === 'number' ? `$${value.toLocaleString()}` : value}
          </div>
          
          {change !== undefined && (
            <div className={cn("flex items-center text-xs", trendColors[trend])}>
              {trendIcons[trend]}
              <span className="ml-1">{formatChange(change)}</span>
            </div>
          )}
          
          {subtitle && (
            <p className="text-xs text-muted-foreground">{subtitle}</p>
          )}
          
          {progress !== undefined && (
            <div className="space-y-1">
              <Progress value={progress} className="h-1" />
              <p className="text-xs text-muted-foreground">
                {progress.toFixed(1)}% of target
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({
  portfolioData,
  positionsData,
  isLoading = false
}) => {
  const metrics = useMemo(() => {
    if (!portfolioData) return null;

    const totalValue = portfolioData.totalPortfolioValueUSDT;
    const spotValue = portfolioData.totalSpotValueUSDT;
    const futuresValue = portfolioData.totalFuturesValueUSDT;
    const spotPnl = portfolioData.spotPnl24hUSDT;
    const spotPnlPercentage = portfolioData.spotPnl24hPercentage;
    const futuresPnl = positionsData?.totalUnrealizedFuturesPnlUSDT || 0;
    
    const totalPnl = spotPnl + futuresPnl;
    const totalPnlPercentage = totalValue > 0 ? (totalPnl / totalValue) * 100 : 0;
    
    const spotAllocation = totalValue > 0 ? (spotValue / totalValue) * 100 : 0;
    const futuresAllocation = totalValue > 0 ? (futuresValue / totalValue) * 100 : 0;
    
    const activePositions = positionsData?.positions?.filter(p => 
      parseFloat(p.positionAmt) !== 0
    ).length || 0;
    
    const diversificationScore = portfolioData.assets?.length || 0;

    return {
      totalValue,
      spotValue,
      futuresValue,
      spotPnl,
      spotPnlPercentage,
      futuresPnl,
      totalPnl,
      totalPnlPercentage,
      spotAllocation,
      futuresAllocation,
      activePositions,
      diversificationScore
    };
  }, [portfolioData, positionsData]);

  if (isLoading || !metrics) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="space-y-0 pb-2">
              <div className="h-4 bg-muted rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded w-full mb-2"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Primary Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Portfolio"
          value={metrics.totalValue}
          change={metrics.totalPnlPercentage}
          icon={<DollarSign className="h-4 w-4" />}
          trend={metrics.totalPnl >= 0 ? 'up' : 'down'}
          subtitle="24h P&L included"
        />
        
        <MetricCard
          title="Spot Holdings"
          value={metrics.spotValue}
          change={metrics.spotPnlPercentage}
          icon={<PieChart className="h-4 w-4" />}
          trend={metrics.spotPnl >= 0 ? 'up' : 'down'}
          subtitle={`${metrics.spotAllocation.toFixed(1)}% allocation`}
          progress={metrics.spotAllocation}
        />
        
        <MetricCard
          title="Futures Value"
          value={metrics.futuresValue}
          change={metrics.futuresPnl}
          changeType="absolute"
          icon={<BarChart3 className="h-4 w-4" />}
          trend={metrics.futuresPnl >= 0 ? 'up' : 'down'}
          subtitle={`${metrics.futuresAllocation.toFixed(1)}% allocation`}
          progress={metrics.futuresAllocation}
        />
        
        <MetricCard
          title="24h P&L"
          value={`${metrics.totalPnl >= 0 ? '+' : ''}$${Math.abs(metrics.totalPnl).toLocaleString()}`}
          change={metrics.totalPnlPercentage}
          icon={<TrendingUp className="h-4 w-4" />}
          trend={metrics.totalPnl >= 0 ? 'up' : 'down'}
          subtitle="Combined spot + futures"
          className={metrics.totalPnl >= 0 ? 'border-green-500/20' : 'border-red-500/20'}
        />
      </div>

      {/* Secondary Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Active Positions"
          value={metrics.activePositions}
          icon={<Target className="h-4 w-4" />}
          subtitle="Open futures positions"
        />
        
        <MetricCard
          title="Asset Diversity"
          value={metrics.diversificationScore}
          icon={<Activity className="h-4 w-4" />}
          subtitle="Different assets held"
          progress={Math.min((metrics.diversificationScore / 20) * 100, 100)}
        />
        
        <MetricCard
          title="Spot P&L"
          value={`${metrics.spotPnl >= 0 ? '+' : ''}$${Math.abs(metrics.spotPnl).toLocaleString()}`}
          change={metrics.spotPnlPercentage}
          icon={<PieChart className="h-4 w-4" />}
          trend={metrics.spotPnl >= 0 ? 'up' : 'down'}
          subtitle="24h spot trading"
        />
        
        <MetricCard
          title="Futures P&L"
          value={`${metrics.futuresPnl >= 0 ? '+' : ''}$${Math.abs(metrics.futuresPnl).toLocaleString()}`}
          icon={<Zap className="h-4 w-4" />}
          trend={metrics.futuresPnl >= 0 ? 'up' : 'down'}
          subtitle="Unrealized futures"
        />
      </div>

      {/* Performance Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Spot Allocation</span>
                <span className="font-medium">{metrics.spotAllocation.toFixed(1)}%</span>
              </div>
              <Progress value={metrics.spotAllocation} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Futures Allocation</span>
                <span className="font-medium">{metrics.futuresAllocation.toFixed(1)}%</span>
              </div>
              <Progress value={metrics.futuresAllocation} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Portfolio Health</span>
                <Badge variant={metrics.totalPnl >= 0 ? 'default' : 'destructive'}>
                  {metrics.totalPnl >= 0 ? 'Profitable' : 'Loss'}
                </Badge>
              </div>
              <div className="text-xs text-muted-foreground">
                Based on 24h performance and diversification
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
