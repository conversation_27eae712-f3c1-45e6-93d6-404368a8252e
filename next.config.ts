import type {NextConfig} from 'next';

const nextConfig: NextConfig = {
  /* config options here */
  typescript: {
    ignoreBuildErrors: false, // Enable TypeScript checking for production
  },
  eslint: {
    ignoreDuringBuilds: false, // Enable ESLint checking for production
  },
  // Server external packages (moved from experimental)
  serverExternalPackages: ['sharp'],

  // Webpack configuration for better chunk handling
  webpack: (config, { dev, isServer, webpack }) => {
    // Improve chunk splitting and loading
    if (!isServer) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks?.cacheGroups,
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 10,
            },
            common: {
              name: 'common',
              minChunks: 2,
              chunks: 'all',
              priority: 5,
              reuseExistingChunk: true,
            },
          },
        },
      };
    }

    // Improve Hot Module Replacement (HMR) stability in development
    if (dev && !isServer) {
      config.plugins.push(
        new webpack.HotModuleReplacementPlugin({
          multiStep: true
        })
      );
      
      // Add better error handling for HMR
      config.optimization.moduleIds = 'named';
      config.optimization.chunkIds = 'named';
    }

    // Add error handling for chunk loading
    if (!dev && !isServer) {
      const originalEntry = config.entry;
      config.entry = async () => {
        const entries = await originalEntry();
        if (entries['main.js'] && !entries['main.js'].includes('./src/lib/chunk-error-handler.js')) {
          entries['main.js'].unshift('./src/lib/chunk-error-handler.js');
        }
        return entries;
      };
    }

    return config;
  },

  // Simplified experimental features
  experimental: {
    optimizePackageImports: [
      'lucide-react',
      'recharts',
      'date-fns',
    ],
    webVitalsAttribution: ['CLS', 'LCP'],
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn.jsdelivr.net',
        port: '',
        pathname: '/gh/atomiclabs/cryptocurrency-icons/**',
      },
      {
        protocol: 'https',
        hostname: 'cryptoicons.org',
        port: '',
        pathname: '/api/**',
      },
      {
        protocol: 'https',
        hostname: 'assets.coincap.io',
        port: '',
        pathname: '/assets/**',
      },
    ],
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 31536000, // 1 ano
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  // Production optimizations
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false,
    reactRemoveProperties: process.env.NODE_ENV === 'production',
    styledComponents: true,
  },
  // Bundle analyzer
  ...(process.env.ANALYZE === 'true' && {
    webpack: (config: any) => {
      config.plugins.push(
        new (require('@next/bundle-analyzer'))({
          enabled: true,
        })
      );
      return config;
    },
  }),
  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, s-maxage=60, stale-while-revalidate=300'
          }
        ]
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      }
    ];
  },
  // Output configuration for Docker deployments
  output: 'standalone',
  // Configuração de runtime
  poweredByHeader: false,
  // Otimizações de build
  compress: true,
  devIndicators: {
    position: 'bottom-left',
  },
  // Configurações específicas para desenvolvimento
  ...(process.env.NODE_ENV === 'development' && {
    // Melhor handling de hot reload
    onDemandEntries: {
      // Período que as páginas ficam no buffer
      maxInactiveAge: 25 * 1000,
      // Páginas que devem ser mantidas quando inactive
      pagesBufferLength: 2,
    },
  }),
};

export default nextConfig;

    