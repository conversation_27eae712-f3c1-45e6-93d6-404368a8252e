#!/usr/bin/env node

const { exec, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧹 Limpando cache do Next.js...');

// Remove o diretório .next se existir
const nextDir = path.join(process.cwd(), '.next');
if (fs.existsSync(nextDir)) {
  fs.rmSync(nextDir, { recursive: true, force: true });
  console.log('✅ Cache .next removido');
}

// Remove o arquivo de build info se existir
const buildInfoFile = path.join(process.cwd(), 'tsconfig.tsbuildinfo');
if (fs.existsSync(buildInfoFile)) {
  fs.unlinkSync(buildInfoFile);
  console.log('✅ TypeScript build info removido');
}

console.log('🚀 Iniciando servidor de desenvolvimento...');

// Inicia o servidor Next.js
const nextProcess = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  shell: true,
  env: {
    ...process.env,
    NODE_ENV: 'development',
    FORCE_COLOR: '1'
  }
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n📦 Encerrando servidor...');
  nextProcess.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  nextProcess.kill('SIGTERM');
  process.exit(0);
});

nextProcess.on('exit', (code) => {
  console.log(`\n📦 Servidor encerrado com código: ${code}`);
  process.exit(code);
}); 