// Chunk loading error handler for Next.js
// This script automatically retries failed chunk loads

(function() {
  'use strict';

  let retryCount = 0;
  const maxRetries = 3;
  const retryDelay = 1000; // 1 second

  // Store original webpack require function
  const originalRequire = window.__webpack_require__;
  
  if (typeof originalRequire !== 'undefined') {
    // Override webpack's chunk loading error handler
    const originalEnsure = originalRequire.e;
    
    if (originalEnsure) {
      originalRequire.e = function(chunkId) {
        return originalEnsure.call(this, chunkId).catch(function(error) {
          console.warn('Chunk loading failed for chunk:', chunkId, error);
          
          if (retryCount < maxRetries) {
            retryCount++;
            console.log(`Retrying chunk load (attempt ${retryCount}/${maxRetries})...`);
            
            return new Promise(function(resolve) {
              setTimeout(function() {
                resolve(originalEnsure.call(originalRequire, chunkId));
              }, retryDelay * retryCount);
            });
          } else {
            console.error('Max retries reached for chunk:', chunkId);
            // Show user-friendly error message
            showChunkErrorMessage();
            throw error;
          }
        });
      };
    }
  }

  // Handle general chunk loading errors
  window.addEventListener('error', function(event) {
    if (event.message && (
        event.message.includes('Loading chunk') || 
        event.message.includes('ChunkLoadError') ||
        event.message.includes('webpack.hot-update.json')
      )) {
      console.warn('Chunk loading error detected:', event.message);
      
      // Don't reload on hot-update 404s during development
      if (event.message.includes('webpack.hot-update.json') && 
          window.location.hostname === 'localhost') {
        console.log('Hot reload file not found - this is normal during development');
        event.preventDefault();
        return false;
      }
      
      if (retryCount < maxRetries) {
        retryCount++;
        console.log(`Attempting to reload page (attempt ${retryCount}/${maxRetries})...`);
        
        setTimeout(function() {
          window.location.reload();
        }, retryDelay);
        
        event.preventDefault();
        return false;
      } else {
        showChunkErrorMessage();
      }
    }
  });

  // Handle unhandled promise rejections (chunk loading failures)
  window.addEventListener('unhandledrejection', function(event) {
    if (event.reason && event.reason.message && 
        (event.reason.message.includes('Loading chunk') || 
         event.reason.message.includes('ChunkLoadError'))) {
      
      console.warn('Chunk loading promise rejection:', event.reason);
      
      if (retryCount < maxRetries) {
        retryCount++;
        console.log(`Attempting to reload page (attempt ${retryCount}/${maxRetries})...`);
        
        setTimeout(function() {
          window.location.reload();
        }, retryDelay);
        
        event.preventDefault();
        return false;
      } else {
        showChunkErrorMessage();
      }
    }
  });

  function showChunkErrorMessage() {
    // Create a user-friendly error message
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    errorDiv.innerHTML = `
      <div style="
        background: white;
        padding: 2rem;
        border-radius: 8px;
        max-width: 500px;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      ">
        <h2 style="color: #dc2626; margin-bottom: 1rem;">Loading Error</h2>
        <p style="margin-bottom: 1.5rem; color: #374151;">
          We're having trouble loading some parts of the application. 
          This might be due to a network issue or a temporary problem.
        </p>
        <button onclick="window.location.reload()" style="
          background: #3b82f6;
          color: white;
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 4px;
          cursor: pointer;
          font-size: 1rem;
          margin-right: 0.5rem;
        ">
          Reload Page
        </button>
        <button onclick="window.location.href='/'" style="
          background: #6b7280;
          color: white;
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 4px;
          cursor: pointer;
          font-size: 1rem;
        ">
          Go Home
        </button>
      </div>
    `;

    document.body.appendChild(errorDiv);
  }

  console.log('Chunk error handler initialized');
})();
