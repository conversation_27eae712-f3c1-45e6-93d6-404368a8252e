
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import Binance from 'node-binance-api';

export async function GET(_request: NextRequest) {
  return NextResponse.json({ 
    success: true, 
    message: "Test connection endpoint is working. Use POST method with API keys for full testing.",
    data: {
      status: "online",
      timestamp: new Date().toISOString()
    }
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { apiKey, apiSecret } = body;

    if (!apiKey || !apiSecret || apiKey.trim() === '' || apiSecret.trim() === '') {
      return NextResponse.json({ success: false, message: "Chave de API ou Segredo da API ausentes ou inválidos." }, { status: 400 });
    }

    const binance = new Binance().options({
      APIKEY: apiKey,
      APISECRET: apiSecret,
    });

    try {
      const accountInfo = await binance.account();
      
      if (accountInfo && Object.keys(accountInfo).length > 0 && !(accountInfo as any).code) {
        const accountType = accountInfo.accountType || "Não disponível";
        const canTrade = accountInfo.canTrade === true;
        let currentBTCPrice: string | null = null;
        let priceError: string | null = null;

        try {
          const prices = await binance.prices('BTCUSDT');
          if (prices && prices.BTCUSDT) {
            currentBTCPrice = parseFloat(String(prices.BTCUSDT)).toLocaleString(undefined, { style: 'currency', currency: 'USD' });
          } else {
            priceError = "Não foi possível obter o preço do BTCUSDT.";
          }
        } catch (priceFetchError: any) {
          console.error("Erro ao buscar preço do BTCUSDT:", priceFetchError?.body || priceFetchError?.message || priceFetchError);
          priceError = "Falha ao buscar preço do BTCUSDT.";
        }

        return NextResponse.json({ 
          success: true, 
          message: "Conexão com a Binance bem-sucedida. Informações da conta e preço do BTC obtidos.",
          data: {
            accountType: accountType,
            canTrade: canTrade,
            currentBTCPrice: currentBTCPrice,
            priceError: priceError,
          }
        });
      } else if (accountInfo && (accountInfo as any).code) {
        // Handle Binance API specific errors (e.g., invalid API key format, permissions)
        return NextResponse.json({ success: false, message: `Erro da API Binance: ${(accountInfo as any).msg} (Código: ${(accountInfo as any).code})` }, { status: 401 });
      } 
      else {
         return NextResponse.json({ success: false, message: "Chaves parecem válidas, mas não foi possível obter informações detalhadas da conta. Verifique as permissões da API ou a resposta da exchange." }, { status: 403 });
      }
    } catch (apiError: any) {
      console.error("Erro da API da Binance ao testar conexão:", apiError?.body || apiError?.message || apiError);
      let errorMessage = "Falha ao testar conexão com a Binance.";
      let statusCode = 401; // Default to unauthorized

      if (apiError && typeof apiError.body === 'string') {
        try {
            const errorBody = JSON.parse(apiError.body);
            errorMessage = `Erro da Binance: ${errorBody.msg} (Código: ${errorBody.code})`;
            if (errorBody.code === -1022) { // Example: Signature for this request is not valid.
                statusCode = 403; // Forbidden or bad credentials
            } else if (errorBody.code === -2014 || errorBody.code === -2015) { // Example: API-key format invalid / Invalid API-key Id.
                statusCode = 400; // Bad request (invalid keys)
            }
        } catch (parseError) {
            // If apiError.body is not JSON, use it as a string
            errorMessage = `Erro da Binance: ${apiError.body}`;
        }
      } else if (apiError && apiError.message) {
        errorMessage = apiError.message;
      }
      
      return NextResponse.json({ success: false, message: errorMessage }, { status: statusCode });
    }

  } catch (error: any) {
    console.error("Erro interno ao processar teste de conexão com Binance:", error);
    let errorMessage = "Falha interna ao testar conexão com a Binance.";
    if (error instanceof Error) {
        errorMessage = error.message;
    }
    return NextResponse.json({ success: false, message: errorMessage }, { status: 500 });
  }
}
