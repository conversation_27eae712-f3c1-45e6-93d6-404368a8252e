import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import './globals.css';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/layout/AppSidebar';
import { AppHeader } from '@/components/layout/AppHeader';
import { Toaster } from "@/components/ui/toaster";
import { NotificationProvider } from "@/components/ui/notification-system";
import ErrorBoundary from "@/components/ui/error-boundary";
import "@/lib/global-error-handler";

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
  display: 'swap',
  preload: true,
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
  display: 'swap',
  preload: true,
});

export const metadata: Metadata = {
  title: 'CryptoPilot',
  description: 'Your all-in-one cryptocurrency trading platform.',
};

export const viewport = 'width=device-width, initial-scale=1.0';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="//api.binance.com" />
        <link rel="dns-prefetch" href="//data-api.binance.vision" />
        
        <link 
          rel="preload" 
          href="/favicon.ico" 
          as="image" 
          type="image/x-icon"
        />
        
        <style dangerouslySetInnerHTML={{
          __html: `
            /* Critical above-fold styles */
            .critical-layout {
              display: flex;
              min-height: 100vh;
              background-color: hsl(210 40% 9%);
              color: hsl(210 40% 95%);
            }
          `
        }} />
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>

        
        <div className="critical-layout">
          <ErrorBoundary>
            <NotificationProvider>
              <SidebarProvider defaultOpen={true}>
                <AppSidebar />
                <div className="flex flex-col flex-1 min-w-0 critical-layer">
                  <AppHeader />
                  <main className="flex-1 p-4 md:p-6 lg:p-8 overflow-y-auto scroll-container will-change-scroll">
                    {children}
                  </main>
                </div>
              </SidebarProvider>
            </NotificationProvider>
          </ErrorBoundary>
        </div>
        <Toaster />
      </body>
    </html>
  );
}
