
export interface PortfolioAsset {
  id: string;
  name: string;
  symbol: string;
  quantity: number;
  price: number;
  value: number;
  change24h: number; // Percentage
  iconUrl?: string;
  exchange: string;
}

export interface Transaction {
  id: string;
  type: 'buy' | 'sell' | 'deposit' | 'withdraw';
  cryptoSymbol: string;
  cryptoName: string;
  amount: number; // Amount of crypto
  pricePerUnit?: number; // Price per unit at time of transaction
  totalValue?: number; // Total fiat value of transaction
  date: string; // ISO string
  exchange: string;
}

export interface OpenPosition {
  id:string;
  cryptoSymbol: string;
  cryptoName: string;
  type: 'long' | 'short';
  entryPrice: number;
  currentPrice: number;
  quantity: number;
  liquidationPrice?: number;
  margin: number;
  pnl: number; // Profit and Loss
  pnlPercentage: number;
  exchange: string;
}

export interface PriceAlert {
  id: string;
  symbol: string;
  cryptoSymbol?: string;
  cryptoName?: string;
  condition: 'above' | 'below' | 'change_percent';
  targetPrice?: number;
  changePercent?: number;
  timeframe?: '1h' | '24h' | '7d';
  status: 'active' | 'triggered' | 'expired' | 'cancelled';
  createdAt: Date | string;
  triggeredAt?: Date | string;
  triggeredPrice?: number;
  isEnabled: boolean;
  notificationMethod: 'browser' | 'email' | 'both';
}

export interface NewsArticle {
  id: string;
  title: string;
  source: string;
  url: string;
  publishedAt: string; // ISO string
  contentSnippet: string; // A short snippet or the full content if available
}
