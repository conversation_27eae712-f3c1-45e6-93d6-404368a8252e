"use client";

import React, { useState, useC<PERSON>back, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { 
  Bell, 
  Plus, 
  Trash2, 
  TrendingUp, 
  TrendingDown, 
  Target,
  AlertTriangle,
  CheckCircle,
  Clock,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useNotificationHelpers } from '@/components/ui/notification-system';
import { PriceAlert } from '@/lib/types';

interface AdvancedPriceAlertsProps {
  alerts: PriceAlert[];
  onCreateAlert: (alert: Omit<PriceAlert, 'id' | 'createdAt' | 'status'>) => void;
  onUpdateAlert: (id: string, updates: Partial<PriceAlert>) => void;
  onDeleteAlert: (id: string) => void;
  availableSymbols: string[];
  currentPrices: Record<string, string>;
  className?: string;
}

const AlertConditionIcon = ({ condition }: { condition: string }) => {
  switch (condition) {
    case 'above':
      return <TrendingUp className="h-4 w-4 text-green-400" />;
    case 'below':
      return <TrendingDown className="h-4 w-4 text-red-400" />;
    case 'change_percent':
      return <Target className="h-4 w-4 text-blue-400" />;
    default:
      return <Bell className="h-4 w-4" />;
  }
};

const AlertStatusBadge = ({ status }: { status: PriceAlert['status'] }) => {
  const variants = {
    active: { variant: 'default' as const, label: 'Active', icon: Clock },
    triggered: { variant: 'destructive' as const, label: 'Triggered', icon: CheckCircle },
    expired: { variant: 'secondary' as const, label: 'Expired', icon: AlertTriangle },
    cancelled: { variant: 'outline' as const, label: 'Cancelled', icon: Settings }
  };

  const config = variants[status];
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  );
};

export const AdvancedPriceAlerts: React.FC<AdvancedPriceAlertsProps> = ({
  alerts,
  onCreateAlert,
  onUpdateAlert,
  onDeleteAlert,
  availableSymbols,
  currentPrices,
  className
}) => {
  const [isCreating, setIsCreating] = useState(false);
  const [newAlert, setNewAlert] = useState<{
    symbol: string;
    condition: 'above' | 'below' | 'change_percent';
    targetPrice: string;
    changePercent: string;
    timeframe: '1h' | '24h' | '7d';
    isEnabled: boolean;
    notificationMethod: 'browser' | 'email' | 'both';
  }>({
    symbol: '',
    condition: 'above',
    targetPrice: '',
    changePercent: '',
    timeframe: '24h',
    isEnabled: true,
    notificationMethod: 'browser'
  });

  const { notifySuccess, notifyError } = useNotificationHelpers();

  const activeAlerts = useMemo(() => 
    alerts.filter(alert => alert.status === 'active' && alert.isEnabled), 
    [alerts]
  );

  const recentlyTriggered = useMemo(() =>
    alerts.filter(alert => alert.status === 'triggered')
      .sort((a, b) => {
        const aTime = a.triggeredAt ? (typeof a.triggeredAt === 'string' ? new Date(a.triggeredAt).getTime() : a.triggeredAt.getTime()) : 0;
        const bTime = b.triggeredAt ? (typeof b.triggeredAt === 'string' ? new Date(b.triggeredAt).getTime() : b.triggeredAt.getTime()) : 0;
        return bTime - aTime;
      })
      .slice(0, 5),
    [alerts]
  );

  const handleCreateAlert = useCallback(() => {
    if (!newAlert.symbol) {
      notifyError("Validation Error", "Please select a symbol");
      return;
    }

    if ((newAlert.condition === 'above' || newAlert.condition === 'below') && !newAlert.targetPrice) {
      notifyError("Validation Error", "Please enter a target price");
      return;
    }

    if (newAlert.condition === 'change_percent' && !newAlert.changePercent) {
      notifyError("Validation Error", "Please enter a change percentage");
      return;
    }

    const alertData = {
      symbol: newAlert.symbol,
      condition: newAlert.condition,
      ...(newAlert.condition === 'change_percent'
        ? { changePercent: parseFloat(newAlert.changePercent || '0'), timeframe: newAlert.timeframe }
        : { targetPrice: parseFloat(newAlert.targetPrice || '0') }
      ),
      isEnabled: newAlert.isEnabled,
      notificationMethod: newAlert.notificationMethod
    };

    onCreateAlert(alertData);
    
    // Reset form
    setNewAlert({
      symbol: '',
      condition: 'above',
      targetPrice: '',
      changePercent: '',
      timeframe: '24h',
      isEnabled: true,
      notificationMethod: 'browser'
    });
    setIsCreating(false);

    notifySuccess("Alert Created", `Price alert for ${newAlert.symbol} has been created`);
  }, [newAlert, onCreateAlert, notifySuccess, notifyError]);

  const handleToggleAlert = useCallback((id: string, isEnabled: boolean) => {
    onUpdateAlert(id, { isEnabled });
    notifySuccess(
      isEnabled ? "Alert Enabled" : "Alert Disabled", 
      `Alert has been ${isEnabled ? 'enabled' : 'disabled'}`
    );
  }, [onUpdateAlert, notifySuccess]);

  const handleDeleteAlert = useCallback((id: string) => {
    onDeleteAlert(id);
    notifySuccess("Alert Deleted", "Price alert has been removed");
  }, [onDeleteAlert, notifySuccess]);

  const getCurrentPrice = (symbol: string) => {
    return currentPrices[symbol] ? parseFloat(currentPrices[symbol]) : null;
  };

  const formatConditionText = (alert: PriceAlert) => {
    const currentPrice = getCurrentPrice(alert.symbol);
    
    switch (alert.condition) {
      case 'above':
        return `Above $${alert.targetPrice?.toLocaleString()}`;
      case 'below':
        return `Below $${alert.targetPrice?.toLocaleString()}`;
      case 'change_percent':
        return `${alert.changePercent}% change in ${alert.timeframe}`;
      default:
        return 'Unknown condition';
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Alerts</p>
                <p className="text-2xl font-bold">{activeAlerts.length}</p>
              </div>
              <Bell className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Recently Triggered</p>
                <p className="text-2xl font-bold">{recentlyTriggered.length}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Alerts</p>
                <p className="text-2xl font-bold">{alerts.length}</p>
              </div>
              <Target className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Create Alert Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Create Price Alert
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsCreating(!isCreating)}
            >
              {isCreating ? 'Cancel' : 'New Alert'}
            </Button>
          </div>
        </CardHeader>

        {isCreating && (
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="symbol">Symbol</Label>
                <Select value={newAlert.symbol} onValueChange={(value) => 
                  setNewAlert(prev => ({ ...prev, symbol: value }))
                }>
                  <SelectTrigger>
                    <SelectValue placeholder="Select symbol" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableSymbols.map(symbol => (
                      <SelectItem key={symbol} value={symbol}>
                        {symbol} {currentPrices[symbol] && `($${parseFloat(currentPrices[symbol]).toLocaleString()})`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="condition">Condition</Label>
                <Select value={newAlert.condition} onValueChange={(value: any) => 
                  setNewAlert(prev => ({ ...prev, condition: value }))
                }>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="above">Price Above</SelectItem>
                    <SelectItem value="below">Price Below</SelectItem>
                    <SelectItem value="change_percent">Percentage Change</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {newAlert.condition === 'change_percent' ? (
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="changePercent">Change Percentage (%)</Label>
                  <Input
                    id="changePercent"
                    type="number"
                    placeholder="e.g., 5"
                    value={newAlert.changePercent}
                    onChange={(e) => setNewAlert(prev => ({ ...prev, changePercent: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timeframe">Timeframe</Label>
                  <Select value={newAlert.timeframe} onValueChange={(value: any) => 
                    setNewAlert(prev => ({ ...prev, timeframe: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1h">1 Hour</SelectItem>
                      <SelectItem value="24h">24 Hours</SelectItem>
                      <SelectItem value="7d">7 Days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <Label htmlFor="targetPrice">Target Price ($)</Label>
                <Input
                  id="targetPrice"
                  type="number"
                  placeholder="Enter target price"
                  value={newAlert.targetPrice}
                  onChange={(e) => setNewAlert(prev => ({ ...prev, targetPrice: e.target.value }))}
                />
                {newAlert.symbol && currentPrices[newAlert.symbol] && (
                  <p className="text-xs text-muted-foreground">
                    Current price: ${parseFloat(currentPrices[newAlert.symbol]).toLocaleString()}
                  </p>
                )}
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  id="enabled"
                  checked={newAlert.isEnabled}
                  onCheckedChange={(checked) => setNewAlert(prev => ({ ...prev, isEnabled: checked }))}
                />
                <Label htmlFor="enabled">Enable alert</Label>
              </div>

              <Button onClick={handleCreateAlert}>
                Create Alert
              </Button>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Active Alerts */}
      {activeAlerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Active Alerts ({activeAlerts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {activeAlerts.map(alert => (
                <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <AlertConditionIcon condition={alert.condition} />
                    <div>
                      <div className="font-medium">{alert.symbol}</div>
                      <div className="text-sm text-muted-foreground">
                        {formatConditionText(alert)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <AlertStatusBadge status={alert.status} />
                    <Switch
                      checked={alert.isEnabled}
                      onCheckedChange={(checked) => handleToggleAlert(alert.id, checked)}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteAlert(alert.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recently Triggered */}
      {recentlyTriggered.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Recently Triggered
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentlyTriggered.map(alert => (
                <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg bg-muted/30">
                  <div className="flex items-center gap-3">
                    <AlertConditionIcon condition={alert.condition} />
                    <div>
                      <div className="font-medium">{alert.symbol}</div>
                      <div className="text-sm text-muted-foreground">
                        {formatConditionText(alert)}
                      </div>
                      {alert.triggeredAt && (
                        <div className="text-xs text-muted-foreground">
                          Triggered: {typeof alert.triggeredAt === 'string' ? new Date(alert.triggeredAt).toLocaleString() : alert.triggeredAt.toLocaleString()}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <AlertStatusBadge status={alert.status} />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteAlert(alert.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {alerts.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Bell className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">No Price Alerts</h3>
            <p className="text-muted-foreground mb-4">
              Create your first price alert to get notified when your target prices are reached.
            </p>
            <Button onClick={() => setIsCreating(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create First Alert
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
