import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Intercepta requests para arquivos webpack hot-update que não existem
  if (pathname.includes('webpack.hot-update.json') || 
      pathname.includes('.hot-update.js') ||
      pathname.includes('.hot-update.json')) {
    
    // Durante desenvolvimento, retorna uma resposta vazia em vez de 404
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔥 Hot-update file not found (normal): ${pathname}`);
      return new NextResponse(JSON.stringify({ status: 'not-found', reason: 'hot-update-file' }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      });
    }
  }

  // Intercepta requests para arquivos estáticos que podem estar em cache
  if (pathname.startsWith('/_next/static/') && 
      !pathname.includes('.hot-update')) {
    
    const response = NextResponse.next();
    
    // Define headers de cache apropriados para arquivos estáticos
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
    
    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * But include hot-update files for special handling
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
    '/_next/static/webpack/:path*.hot-update.json',
    '/_next/static/webpack/:path*.hot-update.js',
  ],
}; 