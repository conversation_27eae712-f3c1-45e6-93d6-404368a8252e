import dynamic from 'next/dynamic';
import { ComponentType } from 'react';

interface DynamicImportOptions {
  loading?: ComponentType;
  ssr?: boolean;
  retries?: number;
}

/**
 * Enhanced dynamic import helper with automatic retry and error handling
 */
export function createDynamicImport<T = any>(
  importFn: () => Promise<{ [key: string]: ComponentType<T> }>,
  componentName: string,
  options: DynamicImportOptions = {}
) {
  const { loading, ssr = false, retries = 3 } = options;

  return dynamic(
    async () => {
      let lastError: Error | null = null;
      
      for (let attempt = 1; attempt <= retries; attempt++) {
        try {
          const module = await importFn();
          
          // Handle both default and named exports
          if (module.default) {
            return { default: module.default };
          } else if (module[componentName]) {
            return { default: module[componentName] };
          } else {
            throw new Error(`Component "${componentName}" not found in module`);
          }
        } catch (error) {
          lastError = error as Error;
          console.warn(`Dynamic import attempt ${attempt}/${retries} failed for ${componentName}:`, error);
          
          if (attempt < retries) {
            // Wait before retrying (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
          }
        }
      }
      
      // If all retries failed, throw the last error
      throw lastError || new Error(`Failed to import ${componentName} after ${retries} attempts`);
    },
    {
      loading: loading || (() => null),
      ssr
    }
  );
}

/**
 * Simplified dynamic import for components with default exports
 */
export function createSimpleDynamicImport<T = any>(
  importPath: string,
  options: DynamicImportOptions = {}
) {
  const { loading, ssr = false } = options;

  return dynamic(() => import(importPath), {
    loading: loading || (() => null),
    ssr
  });
}

/**
 * Dynamic import with automatic error boundary
 */
export function createSafeDynamicImport<T = any>(
  importFn: () => Promise<{ [key: string]: ComponentType<T> }>,
  componentName: string,
  options: DynamicImportOptions & { fallback?: ComponentType } = {}
) {
  const { fallback, ...dynamicOptions } = options;
  
  const DynamicComponent = createDynamicImport(importFn, componentName, dynamicOptions);
  
  if (!fallback) {
    return DynamicComponent;
  }
  
  // Wrap with error boundary
  return dynamic(
    async () => {
      try {
        const module = await importFn();
        return { default: module[componentName] || module.default };
      } catch (error) {
        console.error(`Failed to load ${componentName}, using fallback:`, error);
        return { default: fallback };
      }
    },
    dynamicOptions
  );
}
