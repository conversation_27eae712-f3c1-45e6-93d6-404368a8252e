// Global error handler for chunk loading and other runtime errors
"use client";

export function initializeGlobalErrorHandler() {
  if (typeof window === 'undefined') return;

  // Handle chunk loading errors globally
  window.addEventListener('error', (event) => {
    const { message, filename, error } = event;
    
    // Check if it's a chunk loading error
    if (
      message?.includes('Loading chunk') ||
      message?.includes('ChunkLoadError') ||
      filename?.includes('_next/static/chunks/')
    ) {
      console.warn('Chunk loading error detected, attempting recovery:', {
        message,
        filename,
        error
      });
      
      // Attempt to reload the page after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 1000);
      
      // Prevent the error from propagating
      event.preventDefault();
      return false;
    }
  });

  // Handle unhandled promise rejections (including chunk loading failures)
  window.addEventListener('unhandledrejection', (event) => {
    const { reason } = event;
    
    if (
      reason?.message?.includes('Loading chunk') ||
      reason?.message?.includes('ChunkLoadError') ||
      reason?.name === 'ChunkLoadError'
    ) {
      console.warn('Chunk loading promise rejection detected, attempting recovery:', reason);
      
      // Attempt to reload the page after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 1000);
      
      // Prevent the error from propagating
      event.preventDefault();
      return false;
    }
  });

  // Add a service worker to handle chunk loading failures
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data?.type === 'CHUNK_LOAD_ERROR') {
        console.warn('Service worker reported chunk loading error');
        window.location.reload();
      }
    });
  }

  console.log('Global error handler initialized');
}

// Auto-initialize when this module is imported
if (typeof window !== 'undefined') {
  initializeGlobalErrorHandler();
}
